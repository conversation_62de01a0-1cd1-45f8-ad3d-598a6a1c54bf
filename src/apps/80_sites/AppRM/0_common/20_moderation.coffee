moderation = INCLUDE 'libapp.contentModeration'
llmModeration = INCLUDE 'libapp.llmModeration'
checkContent = moderation.checkContent
textModeration = moderation.textModeration
imageModeration = moderation.imageModeration
ModerationLog = COLLECTION 'chome', 'moderation_log'
config = CONFIG(['serverBase'])
if config.serverBase?.masterMode
  ModerationLog.createIndex( { 'ts': 1 }, { expireAfterSeconds: 3600*24*182 } )

addLog = (ret,params,cb) ->
  insertData =
    ts: new Date()
    uid: params.user?._id
    col: params.collection
    labels: ret.labels
  insertData.id = params.id if params.id
  ModerationLog.insertOne insertData,(err)->
    cb err,ret

contentModeration = (params, cb) ->
  checkContent params,(err, ret) ->
    return cb err,ret unless ret?.block
    addLog ret,params,(err,ret)->
      cb err,ret
DEF 'contentModeration', contentModeration

###
文本内容审核函数
支持两种调用方式：
1. checkTextContent(params, cb) - 使用原有的阿里云审核（向后兼容）
2. checkTextContent(params, promptTemplates, cb) - 使用LLM审核

params: 可以是字符串、字符串数组或包含content的对象
promptTemplates: LLM审核模板数组（可选）
cb: 回调函数 (err, ret) -> ret格式: {block: boolean, labels: Array, msg: string}
###
checkTextContent = (params, promptTemplates, cb) ->
  # 向后兼容性检查：如果只传了两个参数，第二个参数是回调函数
  if typeof promptTemplates is 'function'
    cb = promptTemplates
    promptTemplates = null

  # 检查bypass条件
  return cb(null,null) if (params.bypassAll or params.user?.noFltr)

  # 如果没有提供promptTemplates，使用原有的审核方法
  if not promptTemplates.length
    textModeration params,(err,ret)->
      return cb err,ret unless ret?.block
      addLog ret,params,(err,ret)->
        return cb err,ret
  else
    # 使用LLM审核
    # 参数适配：提取content和options
    if typeof params is 'string'
      content = params
      options = {}
    else if Array.isArray(params)
      content = params
      options = {}
    else
      content = params.content
      options = {
        bypass: params.bypass
        l10n: params.l10n
      }

    try
      # 调用LLM文本审核
      ret = await llmModeration.textModerationLLM(content, promptTemplates, options)
    catch error
      # LLM审核失败时的错误处理
      console.error 'LLM文本审核失败，错误:', error
      return cb error, null
    return cb(null, ret) unless ret?.block
    # 记录审核日志
    addLog ret, params, (err, ret) ->
      cb err, ret


DEF 'checkTextContent', checkTextContent

###
图片内容审核函数
支持两种调用方式：
1. checkImageContent(params, cb) - 使用原有的阿里云审核（向后兼容）
2. checkImageContent(params, promptTemplates, cb) - 使用LLM审核

params: 可以是图片URL字符串或包含content的对象
promptTemplates: LLM审核模板数组（可选）
cb: 回调函数 (err, ret) -> ret格式: {block: boolean, labels: Array, msg: string}
###
checkImageContent = (params, promptTemplates, cb) ->
  # 向后兼容性检查：如果只传了两个参数，第二个参数是回调函数
  if typeof promptTemplates is 'function'
    cb = promptTemplates
    promptTemplates = null

  # 如果没有提供promptTemplates，使用原有的审核方法
  if not promptTemplates.length
    imageModeration params,(err,ret)->
      return cb err,ret unless ret?.block
      return cb err,ret
  else
  # 使用LLM审核
  # 参数适配：提取imageUrl和options
  if typeof params is 'string'
    imageUrl = params
    options = {}
  else
    imageUrl = params.content
    options = {
      bypass: params.bypass
      l10n: params.l10n
    }

  try
    # 调用LLM图片审核
    ret = await llmModeration.imageModerationLLM(imageUrl, promptTemplates, options)
  catch error
    # LLM审核失败时的错误处理
    console.error 'LLM图片审核失败，错误:', error
    return cb error, null
  return cb(null, ret) unless ret?.block

  cb null, ret


DEF 'checkImageContent', checkImageContent