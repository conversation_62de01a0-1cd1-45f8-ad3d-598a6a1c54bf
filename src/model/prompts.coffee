###
Prompts Model
用于管理LLM提示词模板的完整CRUD操作

功能特性:
- 支持提示词模板的增删改查操作
- 使用upsert模式实现存在则更新、不存在则插入
- 支持多种查询方式：按ID、场景、状态等
- 自动管理创建和修改时间戳
- 完整的数据验证和错误处理

数据结构:
  _id: String - 唯一标识符
  nm: String - 模板名称
  desc: String - 模板描述
  ver: Number - 版本号
  status: String - 状态 (active/inactive)
  scenario: String - 使用场景
  tags: Array - 标签数组
  m_cfg: Object - 模型配置 {m_nm, params}
  tpl: Object - 模板内容 {main, sys}
  vars: Array - 变量定义数组
  ts: Date - 创建时间
  _mt: Date - 修改时间

索引字段:
  scenario: 1 - 场景查询索引
  status: 1 - 状态查询索引
###

# 导入必要的库和工具函数
{isAlphaNumeric} = INCLUDE 'lib.helpers_string'

# 索引创建错误处理函数
ensureIndexErr = (err) ->
  if err
    throw err unless err.code is 85

# 获取应用配置
appConfig = CONFIG()

# 定义数据库集合
PromptsCol = COLLECTION 'chome', 'prompts'

# 在主服务器模式下创建索引
config = CONFIG(['serverBase'])
if config.serverBase?.masterMode
  PromptsCol.createIndex {'scenario': 1}, ensureIndexErr
  PromptsCol.createIndex {'status': 1}, ensureIndexErr

class Prompts
  ###
  创建新的提示词模板
  @param {Object} data - 模板数据
  @param {String} data._id - 模板ID (必填)
  @param {String} data.nm - 模板名称 (必填)
  @param {String} data.desc - 模板描述 (必填)
  @param {Number} data.ver - 版本号 (必填)
  @param {String} data.status - 状态 (必填)
  @param {String} data.scenario - 使用场景 (必填)
  @param {Array} data.tags - 标签数组 (可选)
  @param {Object} data.m_cfg - 模型配置 (必填)
  @param {Object} data.tpl - 模板内容 (必填)
  @param {Array} data.vars - 变量定义 (可选)
  @return {Object} 创建结果 {success, data, error}
  ###
  @create: (data) ->
    # 参数验证 - 使用early return模式
    return {success: false, error: MSG_STRINGS.BAD_PARAMETER} unless data

    # 验证必填字段
    return {success: false, error: 'ID不能为空'} unless data._id
    return {success: false, error: '名称不能为空'} unless data.nm
    return {success: false, error: '描述不能为空'} unless data.desc
    return {success: false, error: '版本号不能为空'} unless data.ver?
    return {success: false, error: '状态不能为空'} unless data.status
    return {success: false, error: '场景不能为空'} unless data.scenario
    return {success: false, error: '模型配置不能为空'} unless data.m_cfg
    return {success: false, error: '模板内容不能为空'} unless data.tpl

    # 验证字段格式
    return {success: false, error: 'ID格式无效'} unless isAlphaNumeric(data._id, null, 'extended')
    return {success: false, error: '场景格式无效'} unless isAlphaNumeric(data.scenario, null, 'extended')
    return {success: false, error: '状态格式无效'} unless isAlphaNumeric(data.status)

    # 构建插入数据
    insertData = {
      _id: data._id
      nm: data.nm
      desc: data.desc
      ver: data.ver
      status: data.status
      scenario: data.scenario
      tags: data.tags or []
      m_cfg: data.m_cfg
      tpl: data.tpl
      vars: data.vars or []
      ts: new Date()
      _mt: new Date()
    }

    return await PromptsCol.insertOne(insertData)

  ###
  根据ID查询提示词模板
  @param {String} id - 模板ID
  @return {Object} 查询结果 {success, data, error}
  ###
  @findById: (id) ->
    return {success: false, error: 'ID不能为空'} unless id
    return {success: false, error: 'ID格式无效'} unless isAlphaNumeric(id, null, 'extended')

    return await PromptsCol.findOne({_id: id})

  ###
  根据场景查询提示词模板
  @param {String} scenario - 使用场景
  @param {Object} options - 查询选项 {limit, skip, sort}
  @return {Object} 查询结果 {success, data, error}
  ###
  @findByScenario: (scenario, options = {}) ->
    return {success: false, error: '场景不能为空'} unless scenario
    return {success: false, error: '场景格式无效'} unless isAlphaNumeric(scenario, null, 'extended')

    opt = {}
    opt.limit = options.limit if options.limit
    opt.skip = options.skip if options.skip
    opt.sort = options.sort or {_mt: -1}

    return await PromptsCol.findToArray({scenario}, opt)

  ###
  根据状态查询提示词模板
  @param {String} status - 状态
  @param {Object} options - 查询选项 {limit, skip, sort}
  @return {Object} 查询结果 {success, data, error}
  ###
  @findByStatus: (status, options = {}) ->
    return {success: false, error: '状态不能为空'} unless status
    return {success: false, error: '状态格式无效'} unless isAlphaNumeric(status)

    opt = {}
    opt.limit = options.limit if options.limit
    opt.skip = options.skip if options.skip
    opt.sort = options.sort or {_mt: -1}

    return await PromptsCol.findToArray({status}, opt)

  ###
  查询所有提示词模板
  @param {Object} options - 查询选项 {limit, skip, sort, filter}
  @return {Object} 查询结果 {success, data, error}
  ###
  @findAll: (options = {}) ->
    # 应用查询选项
    opt = {}
    opt.limit = options.limit if options.limit
    opt.skip = options.skip if options.skip
    opt.sort = options.sort or {_mt: -1}
    query = options.filter or {}

    return await PromptsCol.findToArray(query, opt)

  ###
  更新提示词模板
  @param {String} id - 模板ID
  @param {Object} updateData - 更新数据
  @return {Object} 更新结果 {success, data, error}
  ###
  @update: (id, updateData) ->
    return {success: false, error: 'ID不能为空'} unless id
    return {success: false, error: 'ID格式无效'} unless isAlphaNumeric(id, null, 'extended')
    return {success: false, error: '更新数据不能为空'} unless updateData

    # 构建更新操作
    update = {
      $set: {
        ...updateData
        _mt: new Date()  # 自动更新修改时间
      }
    }

    return await PromptsCol.updateOne({_id: id}, update)

  ###
  删除提示词模板
  @param {String} id - 模板ID
  @return {Object} 删除结果 {success, data, error}
  ###
  @delete: (id) ->
    return {success: false, error: 'ID不能为空'} unless id
    return {success: false, error: 'ID格式无效'} unless isAlphaNumeric(id, null, 'extended')

    return await PromptsCol.deleteOne({_id: id})

  ###
  Upsert操作 - 存在则更新，不存在则插入
  @param {Object} data - 模板数据
  @return {Object} 操作结果 {success, data, error}
  ###
  @upsert: (data) ->
    # 参数验证
    return {success: false, error: MSG_STRINGS.BAD_PARAMETER} unless data
    return {success: false, error: 'ID不能为空'} unless data._id
    return {success: false, error: 'ID格式无效'} unless isAlphaNumeric(data._id, null, 'extended')

    # 构建upsert数据
    upsertData = {
      $set: {
        ...data
        _mt: new Date()  # 更新修改时间
      }
      $setOnInsert: {
        ts: new Date()  # 仅在插入时设置创建时间
      }
    }

    return await PromptsCol.updateOne(
      {_id: data._id}
      upsertData
      {upsert: true}
    )

# 注册模型
MODEL 'Prompts', Prompts
