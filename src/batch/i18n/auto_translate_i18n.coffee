###
Description:    自动翻译i18n国际化记录的批处理脚本
Usage:          ./start.sh -n auto_translate_i18n -cmd "lib/batchBase.coffee batch/i18n/auto_translate_i18n.coffee --languages zh,zh-cn,kr"
                ./start.sh -n auto_translate_i18n -cmd "lib/batchBase.coffee batch/i18n/auto_translate_i18n.coffee --languages forceKr dryrun kr"
                ./start.sh -n auto_translate_i18n -cmd "lib/batchBase.coffee batch/i18n/auto_translate_i18n.coffee --languages zh-cn --scenario property_translation"

Parameters:
  --languages <list>    指定需要翻译的目标语言列表，用逗号分隔 (例如: zh,zh-cn,kr)
  --forceKr           强制重新翻译韩语，即使已有翻译也会重新翻译
  --scenario <name>    翻译场景类型 (universal_translation, ui_translation, forum_translation, property_translation)
  --dryrun             试运行模式，只显示将要执行的操作而不实际修改数据库

Features:
  - 通过LLM翻译i18n表中未翻译的记录
  - 智能处理带上下文后缀的文本格式（如：'文本内容:上下文标识符'）
  - 中文简繁转换优化：zh和zh-cn可通过JTFTAUTO函数相互转换，减少LLM调用
  - 支持韩语强制重新翻译
  - 使用数据库prompts表中的LLM翻译提示词模板
  - 支持多种翻译场景和AI模型
  - 详细的处理统计和错误报告

Create date:    2025-07-14
Author:         Luo xiaowei
Run frequency:  On demand
###

debug = DEBUG()
speed = INCLUDE 'lib.speed'
helpers = INCLUDE 'lib.helpers'
{JTFTAUTO} = INCLUDE 'lib.i18n'
translatorManagerLib = INCLUDE 'lib.translator/translatorManager'
llmHelper = INCLUDE 'libapp.llmTranslationHelper'
conf = CONFIG(['azure','deepseek','deepL','openAI','gemini','claude','grok','rm'])
translatorManager = translatorManagerLib.createTranslatorManager(conf)

# 数据库集合
I18nCol = COLLECTION 'chome', 'i18n'
PromptsCol = COLLECTION 'chome', 'prompts'

# 命令行参数解析
yargs = require('yargs')(AVGS)
yargs
  .option 'languages', {
    alias: 'l'
    type: 'string'
    description: '目标语言列表，用逗号分隔 (例如: zh,zh-cn,kr)'
    demandOption: true
  }
  .option 'scenario', {
    alias: 's'
    type: 'string'
    description: '翻译场景类型'
    choices: [
      'universal_translation', 'ui_translation',
      'forum_translation', 'property_translation'
    ]
    default: 'property_translation'
  }
  .help('help')

options = yargs.argv

# 速度监控器
speedMeter = speed.createSpeedMeter {
  intervalTriggerCount: 1000,
  intervalCallback: (speedMeter) ->
    debug.info speedMeter.toString()
}

# 全局变量
dryRun = AVGS.indexOf('dryrun') >= 0
forceKr = AVGS.indexOf('forceKr') >= 0
scenario = options.scenario
startTs = new Date()

# 支持的语言列表
SUPPORTED_LANGUAGES = ['zh', 'zh-cn', 'kr']

# 语言显示名称映射
LANGUAGE_DISPLAY_NAMES = {
  'zh': 'Traditional Chinese'
  'zh-cn': 'Chinese'
  'kr': 'Korean'
  'en': 'English'
}

###*
 * 解析和验证目标语言列表
 * @param {String} languagesStr - 逗号分隔的语言列表字符串
 * @returns {Array} 验证后的语言代码数组
###
parseTargetLanguages = (languagesStr) ->
  return [] unless languagesStr

  languages = languagesStr.split(',').map((lang) -> lang.trim()).filter((lang) -> lang.length > 0)

  # 验证语言代码
  invalidLanguages = languages.filter((lang) -> lang not in SUPPORTED_LANGUAGES)
  if invalidLanguages.length > 0
    errorMsg = "不支持的语言代码: #{invalidLanguages.join(', ')}。支持的语言: #{SUPPORTED_LANGUAGES.join(', ')}"
    throw new Error(errorMsg)

  return languages

###*
 * 构建MongoDB查询条件
 * @param {Array} targetLanguages - 目标语言列表
 * @param {Boolean} forceKr - 是否强制重新翻译韩语
 * @returns {Object} MongoDB查询对象
###
buildTranslationQuery = (targetLanguages, forceKr) ->
  query = {}

  # 构建语言缺失条件
  languageConditions = []

  for lang in targetLanguages
    if lang is 'kr' and forceKr
      # 韩语强制重新翻译：包含所有有kr字段或没有kr字段的记录
      continue
    else
      # 正常情况：查找缺少该语言翻译的记录
      condition = {}
      condition[lang] = {$exists: false}
      languageConditions.push(condition)

  # 如果有语言条件，使用$or查询
  if languageConditions.length > 0
    query.$or = languageConditions

  debug.info '构建的查询条件:', JSON.stringify(query, null, 2)
  return query

###*
 * 获取翻译模板
 * @param {String} scenario - 翻译场景
 * @returns {Promise<Array>} 翻译模板列表
###
getTranslationTemplates = (scenario) ->
  templates = await PromptsCol.findToArray({
    scenario: scenario
    status: 'active'
  })

  if templates.length is 0
    debug.warn "未找到场景 #{scenario} 的活跃翻译模板"
    # 尝试使用通用翻译模板作为备选
    templates = await PromptsCol.findToArray({
      scenario: 'universal_translation'
      status: 'active'
    })

  debug.info "获取到 #{templates.length} 个翻译模板"
  return templates

###*
 * 使用JTFTAUTO进行中文简繁转换
 * @param {Object} record - i18n记录
 * @param {Array} targetLanguages - 目标语言列表
 * @returns {Object} 转换结果 {conversions: Object, remainingLanguages: Array, needsZhConversion: Boolean}
###
optimizeChineseTranslation = (record, targetLanguages) ->
  conversions = {}
  remainingLanguages = []
  needsZhConversion = false

  # 检查是否同时需要 zh 和 zh-cn 且都不存在
  needsZh = 'zh' in targetLanguages and not record.zh
  needsZhCn = 'zh-cn' in targetLanguages and not record['zh-cn']

  if needsZh and needsZhCn
    # 同时需要 zh 和 zh-cn 且都不存在：优先翻译 zh-cn，zh 通过转换获得
    remainingLanguages.push('zh-cn')
    needsZhConversion = true
    debug.debug "中文优化: #{record._id} 将翻译 zh-cn，zh 通过转换获得"

    # 处理其他语言
    for lang in targetLanguages
      if lang not in ['zh', 'zh-cn']
        remainingLanguages.push(lang)
  else
    # 原有逻辑：单独处理每种语言
    for lang in targetLanguages
      if lang is 'zh' and not record.zh and record['zh-cn']
        # 从简体转换为繁体
        conversions.zh = JTFTAUTO(record['zh-cn'], 'zh')
        debug.debug "简繁转换: #{record._id} zh-cn -> zh"
      else if lang is 'zh-cn' and not record['zh-cn'] and record.zh
        # 从繁体转换为简体
        conversions['zh-cn'] = JTFTAUTO(record.zh, 'zh_cn')
        debug.debug "简繁转换: #{record._id} zh -> zh-cn"
      else
        # 需要LLM翻译的语言
        remainingLanguages.push(lang)

  return {conversions, remainingLanguages, needsZhConversion}

###*
 * 解析带有上下文后缀的文本格式
 * @param {String} text - 原始文本，可能包含格式：'文本内容:上下文标识符'
 * @returns {Object} 解析结果 {textToTranslate: string, contextSuffix: string, hasContext: boolean}
###
parseTextWithContext = (text) ->
  return {textToTranslate: text, contextSuffix: '', hasContext: false} unless text

  # 检查是否包含冒号分隔符
  colonIndex = text.indexOf(':')

  if colonIndex > 0 and colonIndex < text.length - 1
    # 分离文本和上下文后缀
    textToTranslate = text.substring(0, colonIndex)
    contextSuffix = text.substring(colonIndex + 1)

    debug.debug "解析带上下文的文本: '#{text}' -> 翻译文本: '#{textToTranslate}', 上下文: '#{contextSuffix}'"

    return {
      textToTranslate: textToTranslate
      contextSuffix: contextSuffix
      hasContext: true
    }
  else
    # 普通文本，无上下文后缀
    return {
      textToTranslate: text
      contextSuffix: ''
      hasContext: false
    }

###*
 * 使用LLM翻译文本，支持带上下文后缀的格式
 * @param {String} text - 需要翻译的文本，支持格式：'文本内容:上下文标识符'
 * @param {String} targetLanguage - 目标语言代码
 * @param {Array} templates - 翻译模板列表
 * @returns {Promise<String>} 翻译结果，保持原有格式（如果有上下文后缀）
 *
 * 示例：
 * - 输入：'1 day before:showing'
 * - 输出：'1天前:showing'（假设翻译为中文）
###
translateWithLLM = (text, targetLanguage, templates) ->
  return null unless text and targetLanguage and templates

  # 解析文本格式，分离翻译内容和上下文后缀
  {textToTranslate, contextSuffix, hasContext} = parseTextWithContext(text)

  # 构建翻译选项
  contextText = if hasContext then contextSuffix else ''

  options = {
    sourceLanguage: 'en'
    context: contextText
    scenario: scenario
  }

  # 获取目标语言显示名称
  targetDisplayName = LANGUAGE_DISPLAY_NAMES[targetLanguage] or targetLanguage

  # 调用翻译服务，只翻译实际的文本内容
  result = await translatorManager.translateContent(
    textToTranslate, targetDisplayName, templates, options
  )

  if result.success
    # 如果有上下文后缀，需要重新附加到翻译结果中
    finalTranslation = result.translatedText

    debug.debug "LLM翻译成功: #{text} -> #{finalTranslation} (#{result.usedService})"
    return finalTranslation
  else
    debug.error "LLM翻译失败: #{result.error}"
    return null

###*
 * 处理单个i18n记录
 * @param {Object} record - i18n记录
 * @param {Array} targetLanguages - 目标语言列表
 * @param {Array} templates - 翻译模板列表
 * @param {Boolean} dryRun - 是否为试运行模式
 * @returns {Promise<Object>} 处理结果
###
processRecord = (record, targetLanguages, templates, dryRun) ->
  result = {
    recordId: record._id
    conversions: 0
    translations: 0
    errors: []
    updates: {}
  }

  # 过滤需要处理的语言（排除已存在的翻译，除非是强制重新翻译韩语）
  languagesToProcess = targetLanguages.filter (lang) ->
    if lang is 'kr' and forceKr
      return true  # 韩语强制重新翻译
    return not record[lang]  # 其他语言只处理缺失的

  return result if languagesToProcess.length is 0

  # 中文简繁转换优化
  optimizationResult = optimizeChineseTranslation(record, languagesToProcess)
  {conversions, remainingLanguages, needsZhConversion} = optimizationResult

  # 应用转换结果
  for lang, translatedText of conversions
    result.updates[lang] = translatedText
    result.conversions++

  # LLM翻译剩余语言
  for lang in remainingLanguages
    translatedText = await translateWithLLM(record.orig or record._id, lang, templates)
    console.log("🚀 ~   |  auto_translate_i18n.coffee:320  |  translatedText:", translatedText)
    if translatedText
      result.updates[lang] = translatedText
      result.translations++
    else
      result.errors.push("LLM翻译失败: #{lang}")

  # 如果翻译了 zh-cn 且需要通过转换生成 zh
  if needsZhConversion and result.updates['zh-cn']
    result.updates.zh = JTFTAUTO(result.updates['zh-cn'], 'zh')
    result.conversions++
    debug.debug "LLM翻译后转换: #{record._id} zh-cn -> zh"

  return result

###*
 * 主执行函数
###
main = () ->
  debug.info '开始i18n自动翻译处理...'

  # 解析目标语言
  targetLanguages = parseTargetLanguages(options.languages)
  debug.info "目标语言: #{targetLanguages.join(', ')}"
  debug.info "翻译场景: #{scenario}"
  debug.info "试运行模式: #{dryRun}"
  debug.info "韩语强制重新翻译: #{forceKr}"

  # 获取翻译模板
  templates = await getTranslationTemplates(scenario)
  if templates.length is 0
    debug.error '未找到可用的翻译模板'
    return EXIT 1

  # 构建查询条件
  query = buildTranslationQuery(targetLanguages, forceKr)

  # 查询需要翻译的记录
  projection = {
    _id: 1
    orig: 1
    zh: 1
    'zh-cn': 1
    kr: 1
  }

  debug.info '开始查询需要翻译的记录...'
  cur = await I18nCol.find(query, {projection})
  cur.maxTimeMS(3600000)  # 设置查询超时为1小时
  stream = cur.stream()

  # 流式处理配置
  obj =
  verbose: 1
  stream: stream
  high: 20  # 控制并发数量
  end: (err) ->
    processTs = (new Date().getTime() - startTs.getTime()) / (1000 * 60)
    if err
      debug.error "处理异常退出. 总处理时间 #{processTs.toFixed(2)} 分钟. ", speedMeter.toString()
      return EXIT 1, err

    debug.info "处理完成, 总处理时间 #{processTs.toFixed(2)} 分钟, #{speedMeter.toString()}"
    EXIT 0

  process: (record, cb) ->
    speedMeter.check { processed: 1 }

    # 处理记录
    try
      result = await processRecord(record, targetLanguages, templates, dryRun)
    catch error
      debug.error "处理记录 #{record._id} 失败: #{error.message}"
      speedMeter.check { processFailed: 1 }
      return cb()

    # 检查是否有更新
    if Object.keys(result.updates).length is 0
      debug.debug "记录 #{record._id} 无需更新"
      speedMeter.check { skipped: 1 }
      return cb()

    # 统计转换和翻译数量
    speedMeter.check { conversions: result.conversions }
    speedMeter.check { llmTranslations: result.translations }

    # 记录错误
    if result.errors.length > 0
      debug.error "翻译错误: #{result.errors.join(', ')}"
      speedMeter.check { errors: result.errors.length }

    if dryRun
      debug.info "试运行: 记录 #{record._id} 将更新字段:", Object.keys(result.updates).join(', ')
      speedMeter.check { dryRun: 1 }
      return cb()

    # 执行数据库更新
    updateDoc = {
      $set: result.updates
    }

    try
      await I18nCol.updateOne({_id: record._id}, updateDoc)
      speedMeter.check { updated: 1 }
      debug.debug "成功更新记录 #{record._id}:", Object.keys(result.updates).join(', ')
    catch error
      debug.error "更新记录 #{record._id} 失败: #{error.message}"
      speedMeter.check { updateFailed: 1 }

    return cb()

  # 开始流式处理
  helpers.streaming obj

# 启动主函数
main()